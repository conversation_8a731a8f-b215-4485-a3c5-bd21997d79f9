#!/usr/bin/env python3
"""
STATISTICAL SIGNIFICANCE TESTING FOR SPAM DETECTION MODELS
Addressing Reviewer Comments: Add statistical validation of results
"""

import numpy as np
import pandas as pd
import torch
from scipy import stats
from scipy.stats import ttest_rel, wil<PERSON>xon, friedman<PERSON><PERSON><PERSON>re, mannwhitney<PERSON>
from sklearn.model_selection import StratifiedKFold, cross_val_score
from sklearn.metrics import accuracy_score, f1_score, roc_auc_score
import matplotlib.pyplot as plt
import seaborn as sns
from itertools import combinations
import warnings
warnings.filterwarnings('ignore')

# Import training functions (these would need to be defined or imported)
def train_model(model, data, labels, train_mask, test_mask, epochs=50):
    """Simple training function for statistical testing"""
    optimizer = torch.optim.Adam(model.parameters(), lr=0.01)
    criterion = torch.nn.CrossEntropyLoss()

    model.train()
    for epoch in range(epochs):
        optimizer.zero_grad()
        out = model(data)
        loss = criterion(out[train_mask], labels[train_mask])
        loss.backward()
        optimizer.step()

    return [], [], 0  # Return dummy values for compatibility

def evaluate_model(model, data, labels, test_mask):
    """Simple evaluation function for statistical testing"""
    model.eval()
    with torch.no_grad():
        out = model(data)
        pred = out[test_mask].argmax(dim=1)
        probs = torch.softmax(out[test_mask], dim=1)[:, 1]

    accuracy = accuracy_score(labels[test_mask].cpu(), pred.cpu())
    f1 = f1_score(labels[test_mask].cpu(), pred.cpu())
    auc = roc_auc_score(labels[test_mask].cpu(), probs.cpu())

    return {'accuracy': accuracy, 'f1': f1, 'auc': auc}

class StatisticalTester:
    """
    Comprehensive statistical testing for model comparison
    """
    
    def __init__(self, alpha=0.05):
        self.alpha = alpha
        self.results = {}
        
    def cross_validation_comparison(self, models, data, labels, cv_folds=10, metrics=['accuracy', 'f1', 'auc']):
        """
        Perform cross-validation comparison with statistical testing
        """
        print(f"\n📊 CROSS-VALIDATION COMPARISON ({cv_folds}-fold)")
        print("=" * 60)
        
        cv = StratifiedKFold(n_splits=cv_folds, shuffle=True, random_state=42)
        cv_results = {}
        
        for model_name, model in models.items():
            print(f"  🔄 Cross-validating {model_name}...")
            
            fold_results = {metric: [] for metric in metrics}
            
            for fold, (train_idx, test_idx) in enumerate(cv.split(data.x, labels)):
                # Create masks for this fold
                train_mask = torch.zeros(len(labels), dtype=torch.bool)
                test_mask = torch.zeros(len(labels), dtype=torch.bool)
                train_mask[train_idx] = True
                test_mask[test_idx] = True
                
                # Train model on this fold
                model_copy = type(model)(model.input_dim if hasattr(model, 'input_dim') else data.x.size(1))
                train_losses, val_accs, _ = train_model(model_copy, data, labels, train_mask, test_mask)
                
                # Evaluate on test set
                metrics_fold = evaluate_model(model_copy, data, labels, test_mask)
                
                for metric in metrics:
                    if metric in metrics_fold:
                        fold_results[metric].append(metrics_fold[metric])
            
            cv_results[model_name] = fold_results
            
            # Print summary statistics
            for metric in metrics:
                scores = fold_results[metric]
                mean_score = np.mean(scores)
                std_score = np.std(scores)
                print(f"    {metric}: {mean_score:.4f} ± {std_score:.4f}")
        
        self.cv_results = cv_results
        return cv_results
    
    def pairwise_statistical_tests(self, cv_results, metric='accuracy'):
        """
        Perform pairwise statistical tests between models
        """
        print(f"\n🔬 PAIRWISE STATISTICAL TESTS ({metric.upper()})")
        print("=" * 60)
        
        model_names = list(cv_results.keys())
        n_models = len(model_names)
        
        # Create results matrix
        p_values_matrix = np.ones((n_models, n_models))
        test_statistics = np.zeros((n_models, n_models))
        
        results_df = pd.DataFrame(index=model_names, columns=model_names)
        
        for i, model1 in enumerate(model_names):
            for j, model2 in enumerate(model_names):
                if i != j:
                    scores1 = cv_results[model1][metric]
                    scores2 = cv_results[model2][metric]
                    
                    # Paired t-test (parametric)
                    t_stat, t_pval = ttest_rel(scores1, scores2)
                    
                    # Wilcoxon signed-rank test (non-parametric)
                    w_stat, w_pval = wilcoxon(scores1, scores2, alternative='two-sided')
                    
                    # Use more conservative p-value
                    p_val = max(t_pval, w_pval)
                    
                    p_values_matrix[i, j] = p_val
                    test_statistics[i, j] = t_stat
                    
                    # Determine significance
                    if p_val < self.alpha:
                        significance = "***" if p_val < 0.001 else "**" if p_val < 0.01 else "*"
                        mean_diff = np.mean(scores1) - np.mean(scores2)
                        direction = ">" if mean_diff > 0 else "<"
                        results_df.loc[model1, model2] = f"{direction} {significance}"
                    else:
                        results_df.loc[model1, model2] = "n.s."
                else:
                    results_df.loc[model1, model2] = "-"
        
        print("Pairwise comparison results:")
        print("*** p < 0.001, ** p < 0.01, * p < 0.05, n.s. = not significant")
        print(results_df)
        
        return results_df, p_values_matrix
    
    def friedman_test(self, cv_results, metric='accuracy'):
        """
        Perform Friedman test for multiple model comparison
        """
        print(f"\n🎯 FRIEDMAN TEST ({metric.upper()})")
        print("=" * 50)
        
        model_names = list(cv_results.keys())
        scores_matrix = []
        
        for model_name in model_names:
            scores_matrix.append(cv_results[model_name][metric])
        
        # Transpose to have folds as rows, models as columns
        scores_matrix = np.array(scores_matrix).T
        
        # Perform Friedman test
        statistic, p_value = friedmanchisquare(*scores_matrix.T)
        
        print(f"Friedman test statistic: {statistic:.4f}")
        print(f"p-value: {p_value:.6f}")
        
        if p_value < self.alpha:
            print(f"✅ Significant difference between models (p < {self.alpha})")
            
            # Post-hoc analysis: rank models
            ranks = stats.rankdata(-scores_matrix, axis=1)  # Negative for descending order
            mean_ranks = np.mean(ranks, axis=0)
            
            print("\nModel rankings (lower rank = better performance):")
            for i, model_name in enumerate(model_names):
                print(f"  {model_name}: {mean_ranks[i]:.2f}")
        else:
            print(f"❌ No significant difference between models (p >= {self.alpha})")
        
        return statistic, p_value, mean_ranks if p_value < self.alpha else None
    
    def effect_size_analysis(self, cv_results, metric='accuracy'):
        """
        Calculate effect sizes (Cohen's d) for model comparisons
        """
        print(f"\n📏 EFFECT SIZE ANALYSIS ({metric.upper()})")
        print("=" * 50)
        
        model_names = list(cv_results.keys())
        effect_sizes = {}
        
        for model1, model2 in combinations(model_names, 2):
            scores1 = np.array(cv_results[model1][metric])
            scores2 = np.array(cv_results[model2][metric])
            
            # Calculate Cohen's d
            pooled_std = np.sqrt(((len(scores1) - 1) * np.var(scores1, ddof=1) + 
                                 (len(scores2) - 1) * np.var(scores2, ddof=1)) / 
                                (len(scores1) + len(scores2) - 2))
            
            cohens_d = (np.mean(scores1) - np.mean(scores2)) / pooled_std
            
            effect_sizes[f"{model1} vs {model2}"] = cohens_d
            
            # Interpret effect size
            if abs(cohens_d) < 0.2:
                interpretation = "negligible"
            elif abs(cohens_d) < 0.5:
                interpretation = "small"
            elif abs(cohens_d) < 0.8:
                interpretation = "medium"
            else:
                interpretation = "large"
            
            print(f"  {model1} vs {model2}: d = {cohens_d:.3f} ({interpretation})")
        
        return effect_sizes
    
    def bootstrap_confidence_intervals(self, cv_results, metric='accuracy', n_bootstrap=1000, confidence=0.95):
        """
        Calculate bootstrap confidence intervals for model performance
        """
        print(f"\n🔄 BOOTSTRAP CONFIDENCE INTERVALS ({metric.upper()})")
        print("=" * 60)
        
        alpha_ci = 1 - confidence
        lower_percentile = (alpha_ci / 2) * 100
        upper_percentile = (1 - alpha_ci / 2) * 100
        
        confidence_intervals = {}
        
        for model_name, results in cv_results.items():
            scores = np.array(results[metric])
            
            # Bootstrap sampling
            bootstrap_means = []
            for _ in range(n_bootstrap):
                bootstrap_sample = np.random.choice(scores, size=len(scores), replace=True)
                bootstrap_means.append(np.mean(bootstrap_sample))
            
            # Calculate confidence interval
            ci_lower = np.percentile(bootstrap_means, lower_percentile)
            ci_upper = np.percentile(bootstrap_means, upper_percentile)
            
            confidence_intervals[model_name] = (ci_lower, ci_upper)
            
            print(f"  {model_name}: [{ci_lower:.4f}, {ci_upper:.4f}] ({confidence*100:.0f}% CI)")
        
        return confidence_intervals
    
    def visualize_results(self, cv_results, metric='accuracy', save_path=None):
        """
        Create comprehensive visualization of statistical results
        """
        print(f"\n📊 CREATING STATISTICAL VISUALIZATIONS")
        print("=" * 50)
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        
        # 1. Box plot of cross-validation scores
        model_names = list(cv_results.keys())
        scores_data = [cv_results[model][metric] for model in model_names]
        
        ax1.boxplot(scores_data, labels=model_names)
        ax1.set_title(f'Cross-Validation {metric.capitalize()} Distribution')
        ax1.set_ylabel(metric.capitalize())
        ax1.tick_params(axis='x', rotation=45)
        ax1.grid(True, alpha=0.3)
        
        # 2. Mean performance with error bars
        means = [np.mean(scores) for scores in scores_data]
        stds = [np.std(scores) for scores in scores_data]
        
        ax2.bar(model_names, means, yerr=stds, capsize=5, alpha=0.7)
        ax2.set_title(f'Mean {metric.capitalize()} with Standard Deviation')
        ax2.set_ylabel(metric.capitalize())
        ax2.tick_params(axis='x', rotation=45)
        ax2.grid(True, alpha=0.3)
        
        # 3. Pairwise comparison heatmap
        _, p_values_matrix = self.pairwise_statistical_tests(cv_results, metric)
        
        # Convert p-values to significance levels
        sig_matrix = np.where(p_values_matrix < 0.001, 3,
                     np.where(p_values_matrix < 0.01, 2,
                     np.where(p_values_matrix < 0.05, 1, 0)))
        
        im = ax3.imshow(sig_matrix, cmap='RdYlBu_r', aspect='auto')
        ax3.set_xticks(range(len(model_names)))
        ax3.set_yticks(range(len(model_names)))
        ax3.set_xticklabels(model_names, rotation=45)
        ax3.set_yticklabels(model_names)
        ax3.set_title('Statistical Significance Matrix')
        
        # Add colorbar
        cbar = plt.colorbar(im, ax=ax3)
        cbar.set_label('Significance Level')
        cbar.set_ticks([0, 1, 2, 3])
        cbar.set_ticklabels(['n.s.', 'p<0.05', 'p<0.01', 'p<0.001'])
        
        # 4. Performance ranking
        mean_scores = {model: np.mean(cv_results[model][metric]) for model in model_names}
        sorted_models = sorted(mean_scores.items(), key=lambda x: x[1], reverse=True)
        
        models_sorted = [item[0] for item in sorted_models]
        scores_sorted = [item[1] for item in sorted_models]
        
        colors = plt.cm.viridis(np.linspace(0, 1, len(models_sorted)))
        ax4.barh(models_sorted, scores_sorted, color=colors)
        ax4.set_title(f'Model Ranking by {metric.capitalize()}')
        ax4.set_xlabel(metric.capitalize())
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"  ✅ Visualization saved to {save_path}")
        
        plt.show()
        
        return fig
    
    def comprehensive_statistical_analysis(self, models, data, labels, cv_folds=10):
        """
        Run complete statistical analysis pipeline
        """
        print("\n🔬 COMPREHENSIVE STATISTICAL ANALYSIS")
        print("=" * 60)
        
        # 1. Cross-validation comparison
        cv_results = self.cross_validation_comparison(models, data, labels, cv_folds)
        
        # 2. Statistical tests for each metric
        for metric in ['accuracy', 'f1', 'auc']:
            if all(metric in results for results in cv_results.values()):
                print(f"\n{'='*20} {metric.upper()} ANALYSIS {'='*20}")
                
                # Pairwise tests
                self.pairwise_statistical_tests(cv_results, metric)
                
                # Friedman test
                self.friedman_test(cv_results, metric)
                
                # Effect size analysis
                self.effect_size_analysis(cv_results, metric)
                
                # Bootstrap confidence intervals
                self.bootstrap_confidence_intervals(cv_results, metric)
        
        # 3. Visualizations
        self.visualize_results(cv_results, 'accuracy', 'statistical_analysis.png')
        
        return cv_results

def run_statistical_significance_testing(models, data, labels):
    """
    Main function to run statistical significance testing
    """
    tester = StatisticalTester(alpha=0.05)
    results = tester.comprehensive_statistical_analysis(models, data, labels, cv_folds=10)
    
    print("\n✅ STATISTICAL ANALYSIS COMPLETE")
    print("Key findings:")
    print("  • Cross-validation results provide robust performance estimates")
    print("  • Pairwise tests identify significant differences between models")
    print("  • Effect size analysis quantifies practical significance")
    print("  • Bootstrap confidence intervals provide uncertainty estimates")
    
    return results, tester

if __name__ == "__main__":
    print("🚀 STATISTICAL TESTING MODULE LOADED")
    print("Available functions:")
    print("  • cross_validation_comparison()")
    print("  • pairwise_statistical_tests()")
    print("  • friedman_test()")
    print("  • effect_size_analysis()")
    print("  • bootstrap_confidence_intervals()")
    print("  • comprehensive_statistical_analysis()")
