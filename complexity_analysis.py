#!/usr/bin/env python3
"""
COMPUTATIONAL COMPLEXITY ANALYSIS FOR SPAM DETECTION MODELS
Addressing Reviewer Comments: Add computational complexity analysis
"""

import torch
import time
import psutil
import numpy as np
import matplotlib.pyplot as plt
# from memory_profiler import profile  # Optional dependency
import tracemalloc
from torch.profiler import profile as torch_profile, record_function, ProfilerActivity
import pandas as pd
from typing import Dict, List, Tuple
import warnings
warnings.filterwarnings('ignore')

class ComplexityAnalyzer:
    """
    Comprehensive computational complexity analysis for deep learning models
    """
    
    def __init__(self):
        self.results = {}
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
    def count_parameters(self, model):
        """
        Count total and trainable parameters in the model
        """
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        return {
            'total_parameters': total_params,
            'trainable_parameters': trainable_params,
            'parameter_size_mb': total_params * 4 / (1024 * 1024)  # Assuming float32
        }
    
    def measure_inference_time(self, model, data, num_runs=100, warmup_runs=10):
        """
        Measure inference time with proper GPU synchronization
        """
        model.eval()
        model = model.to(self.device)
        data = data.to(self.device)
        
        # Warmup runs
        with torch.no_grad():
            for _ in range(warmup_runs):
                _ = model(data)
                if self.device.type == 'cuda':
                    torch.cuda.synchronize()
        
        # Actual timing
        times = []
        with torch.no_grad():
            for _ in range(num_runs):
                start_time = time.perf_counter()
                _ = model(data)
                if self.device.type == 'cuda':
                    torch.cuda.synchronize()
                end_time = time.perf_counter()
                times.append(end_time - start_time)
        
        return {
            'mean_inference_time': np.mean(times),
            'std_inference_time': np.std(times),
            'min_inference_time': np.min(times),
            'max_inference_time': np.max(times),
            'throughput_samples_per_second': data.x.size(0) / np.mean(times)
        }
    
    def measure_training_time(self, model, data, labels, train_mask, epochs=10):
        """
        Measure training time per epoch
        """
        model.train()
        model = model.to(self.device)
        data = data.to(self.device)
        labels = labels.to(self.device)
        
        optimizer = torch.optim.Adam(model.parameters(), lr=0.01)
        criterion = torch.nn.CrossEntropyLoss()
        
        epoch_times = []
        
        for epoch in range(epochs):
            start_time = time.perf_counter()
            
            optimizer.zero_grad()
            out = model(data)
            loss = criterion(out[train_mask], labels[train_mask])
            loss.backward()
            optimizer.step()
            
            if self.device.type == 'cuda':
                torch.cuda.synchronize()
            
            end_time = time.perf_counter()
            epoch_times.append(end_time - start_time)
        
        return {
            'mean_epoch_time': np.mean(epoch_times),
            'std_epoch_time': np.std(epoch_times),
            'total_training_time': np.sum(epoch_times)
        }
    
    def measure_memory_usage(self, model, data):
        """
        Measure memory usage during inference and training
        """
        model = model.to(self.device)
        data = data.to(self.device)
        
        # Measure inference memory
        if self.device.type == 'cuda':
            torch.cuda.empty_cache()
            torch.cuda.reset_peak_memory_stats()
            
            model.eval()
            with torch.no_grad():
                _ = model(data)
            
            inference_memory = torch.cuda.max_memory_allocated() / (1024 * 1024)  # MB
        else:
            # For CPU, use tracemalloc
            tracemalloc.start()
            model.eval()
            with torch.no_grad():
                _ = model(data)
            current, peak = tracemalloc.get_traced_memory()
            tracemalloc.stop()
            inference_memory = peak / (1024 * 1024)  # MB
        
        # Measure training memory
        if self.device.type == 'cuda':
            torch.cuda.empty_cache()
            torch.cuda.reset_peak_memory_stats()
            
            model.train()
            optimizer = torch.optim.Adam(model.parameters())
            criterion = torch.nn.CrossEntropyLoss()
            
            out = model(data)
            loss = criterion(out[:10], torch.randint(0, 2, (10,)).to(self.device))
            loss.backward()
            
            training_memory = torch.cuda.max_memory_allocated() / (1024 * 1024)  # MB
        else:
            tracemalloc.start()
            model.train()
            optimizer = torch.optim.Adam(model.parameters())
            criterion = torch.nn.CrossEntropyLoss()
            
            out = model(data)
            loss = criterion(out[:10], torch.randint(0, 2, (10,)))
            loss.backward()
            
            current, peak = tracemalloc.get_traced_memory()
            tracemalloc.stop()
            training_memory = peak / (1024 * 1024)  # MB
        
        return {
            'inference_memory_mb': inference_memory,
            'training_memory_mb': training_memory,
            'memory_efficiency': inference_memory / self.count_parameters(model)['parameter_size_mb']
        }
    
    def theoretical_complexity_analysis(self, model, input_dim, num_nodes, num_edges):
        """
        Analyze theoretical computational complexity
        """
        complexity = {}
        
        # Count different layer types
        linear_layers = sum(1 for module in model.modules() if isinstance(module, torch.nn.Linear))
        conv_layers = sum(1 for module in model.modules() 
                         if hasattr(module, '__class__') and 'Conv' in module.__class__.__name__)
        attention_layers = sum(1 for module in model.modules() 
                              if hasattr(module, '__class__') and 'Attention' in module.__class__.__name__)
        
        # Estimate complexity based on layer types
        # Linear layers: O(input_dim * output_dim * batch_size)
        # Graph convolutions: O(num_edges * feature_dim)
        # Attention: O(num_nodes^2 * feature_dim)
        
        total_params = self.count_parameters(model)['total_parameters']
        
        # Rough estimates
        linear_complexity = linear_layers * input_dim * num_nodes
        graph_complexity = conv_layers * num_edges * input_dim
        attention_complexity = attention_layers * num_nodes * num_nodes * input_dim
        
        complexity = {
            'linear_complexity': linear_complexity,
            'graph_complexity': graph_complexity,
            'attention_complexity': attention_complexity,
            'total_theoretical_ops': linear_complexity + graph_complexity + attention_complexity,
            'space_complexity': total_params,
            'complexity_class': self._determine_complexity_class(
                linear_complexity, graph_complexity, attention_complexity, num_nodes, num_edges
            )
        }
        
        return complexity
    
    def _determine_complexity_class(self, linear_comp, graph_comp, attention_comp, num_nodes, num_edges):
        """
        Determine the dominant complexity class
        """
        if attention_comp > linear_comp and attention_comp > graph_comp:
            return f"O(n²d) - Attention dominant"
        elif graph_comp > linear_comp:
            return f"O(|E|d) - Graph convolution dominant"
        else:
            return f"O(nd) - Linear dominant"
    
    def scalability_analysis(self, model_class, input_dim, node_counts=[100, 500, 1000, 2000, 5000]):
        """
        Analyze how performance scales with dataset size
        """
        print("\n📈 SCALABILITY ANALYSIS")
        print("=" * 50)
        
        scalability_results = {
            'node_counts': [],
            'inference_times': [],
            'memory_usage': [],
            'parameter_counts': []
        }
        
        for num_nodes in node_counts:
            print(f"  Testing with {num_nodes} nodes...")
            
            # Create synthetic data for this scale
            x = torch.randn(num_nodes, input_dim)
            edge_index = torch.randint(0, num_nodes, (2, num_nodes * 2))  # Sparse edges
            data = type('Data', (), {'x': x, 'edge_index': edge_index})()
            
            # Initialize model
            model = model_class(input_dim)
            
            # Measure performance
            try:
                inference_metrics = self.measure_inference_time(model, data, num_runs=10)
                memory_metrics = self.measure_memory_usage(model, data)
                param_metrics = self.count_parameters(model)
                
                scalability_results['node_counts'].append(num_nodes)
                scalability_results['inference_times'].append(inference_metrics['mean_inference_time'])
                scalability_results['memory_usage'].append(memory_metrics['inference_memory_mb'])
                scalability_results['parameter_counts'].append(param_metrics['total_parameters'])
                
            except Exception as e:
                print(f"    ⚠️ Failed at {num_nodes} nodes: {e}")
                break
        
        return scalability_results
    
    def profile_model_operations(self, model, data, output_file="model_profile.json"):
        """
        Detailed profiling of model operations using PyTorch profiler
        """
        model = model.to(self.device)
        data = data.to(self.device)
        
        with torch_profile(
            activities=[ProfilerActivity.CPU, ProfilerActivity.CUDA] if self.device.type == 'cuda' else [ProfilerActivity.CPU],
            record_shapes=True,
            profile_memory=True,
            with_stack=True
        ) as prof:
            with record_function("model_inference"):
                model.eval()
                with torch.no_grad():
                    _ = model(data)
        
        # Export profile
        prof.export_chrome_trace(output_file)
        
        # Get key statistics
        key_stats = prof.key_averages().table(sort_by="cpu_time_total", row_limit=10)
        
        return {
            'profile_file': output_file,
            'key_statistics': key_stats,
            'total_cpu_time': sum([item.cpu_time_total for item in prof.key_averages()]),
            'total_cuda_time': sum([item.cuda_time_total for item in prof.key_averages()]) if self.device.type == 'cuda' else 0
        }
    
    def comprehensive_analysis(self, models, data, labels, train_mask):
        """
        Run comprehensive complexity analysis for all models
        """
        print("\n⚡ COMPREHENSIVE COMPUTATIONAL COMPLEXITY ANALYSIS")
        print("=" * 60)
        
        results = {}
        
        for model_name, model in models.items():
            print(f"\n🔍 Analyzing {model_name}...")
            
            model_results = {}
            
            # Parameter analysis
            model_results['parameters'] = self.count_parameters(model)
            
            # Timing analysis
            model_results['inference'] = self.measure_inference_time(model, data)
            model_results['training'] = self.measure_training_time(model, data, labels, train_mask)
            
            # Memory analysis
            model_results['memory'] = self.measure_memory_usage(model, data)
            
            # Theoretical complexity
            num_nodes = data.x.size(0)
            num_edges = data.edge_index.size(1) if hasattr(data, 'edge_index') else 0
            model_results['theoretical'] = self.theoretical_complexity_analysis(
                model, data.x.size(1), num_nodes, num_edges
            )
            
            # Profiling
            try:
                model_results['profiling'] = self.profile_model_operations(
                    model, data, f"{model_name.lower().replace(' ', '_')}_profile.json"
                )
            except Exception as e:
                print(f"    ⚠️ Profiling failed: {e}")
                model_results['profiling'] = None
            
            results[model_name] = model_results
            
            # Print summary
            params = model_results['parameters']
            inference = model_results['inference']
            memory = model_results['memory']
            
            print(f"    Parameters: {params['total_parameters']:,} ({params['parameter_size_mb']:.1f} MB)")
            print(f"    Inference: {inference['mean_inference_time']*1000:.2f} ms")
            print(f"    Throughput: {inference['throughput_samples_per_second']:.0f} samples/sec")
            print(f"    Memory: {memory['inference_memory_mb']:.1f} MB")
        
        self.results = results
        return results
    
    def visualize_complexity_results(self, results, save_path="complexity_analysis.png"):
        """
        Create comprehensive visualization of complexity results
        """
        print(f"\n📊 CREATING COMPLEXITY VISUALIZATIONS")
        print("=" * 50)
        
        model_names = list(results.keys())
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        
        # 1. Parameter count comparison
        param_counts = [results[model]['parameters']['total_parameters'] for model in model_names]
        ax1.bar(model_names, param_counts)
        ax1.set_title('Model Parameter Count')
        ax1.set_ylabel('Number of Parameters')
        ax1.tick_params(axis='x', rotation=45)
        ax1.set_yscale('log')
        
        # 2. Inference time comparison
        inference_times = [results[model]['inference']['mean_inference_time'] * 1000 for model in model_names]
        ax2.bar(model_names, inference_times, color='orange')
        ax2.set_title('Inference Time')
        ax2.set_ylabel('Time (ms)')
        ax2.tick_params(axis='x', rotation=45)
        
        # 3. Memory usage comparison
        memory_usage = [results[model]['memory']['inference_memory_mb'] for model in model_names]
        ax3.bar(model_names, memory_usage, color='green')
        ax3.set_title('Memory Usage')
        ax3.set_ylabel('Memory (MB)')
        ax3.tick_params(axis='x', rotation=45)
        
        # 4. Efficiency scatter plot (accuracy vs inference time)
        # Note: This would need accuracy data from evaluation
        throughput = [results[model]['inference']['throughput_samples_per_second'] for model in model_names]
        ax4.scatter(inference_times, memory_usage, s=100, alpha=0.7)
        for i, model in enumerate(model_names):
            ax4.annotate(model, (inference_times[i], memory_usage[i]), 
                        xytext=(5, 5), textcoords='offset points', fontsize=8)
        ax4.set_xlabel('Inference Time (ms)')
        ax4.set_ylabel('Memory Usage (MB)')
        ax4.set_title('Efficiency Analysis')
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"  ✅ Visualization saved to {save_path}")
        plt.show()
        
        return fig
    
    def generate_complexity_report(self, results, output_file="complexity_report.txt"):
        """
        Generate detailed complexity analysis report
        """
        with open(output_file, 'w') as f:
            f.write("COMPUTATIONAL COMPLEXITY ANALYSIS REPORT\n")
            f.write("=" * 50 + "\n\n")
            
            for model_name, model_results in results.items():
                f.write(f"{model_name.upper()}\n")
                f.write("-" * len(model_name) + "\n")
                
                # Parameters
                params = model_results['parameters']
                f.write(f"Parameters: {params['total_parameters']:,}\n")
                f.write(f"Model Size: {params['parameter_size_mb']:.1f} MB\n")
                
                # Performance
                inference = model_results['inference']
                f.write(f"Inference Time: {inference['mean_inference_time']*1000:.2f} ± {inference['std_inference_time']*1000:.2f} ms\n")
                f.write(f"Throughput: {inference['throughput_samples_per_second']:.0f} samples/sec\n")
                
                # Memory
                memory = model_results['memory']
                f.write(f"Inference Memory: {memory['inference_memory_mb']:.1f} MB\n")
                f.write(f"Training Memory: {memory['training_memory_mb']:.1f} MB\n")
                
                # Theoretical complexity
                theoretical = model_results['theoretical']
                f.write(f"Complexity Class: {theoretical['complexity_class']}\n")
                
                f.write("\n")
        
        print(f"  ✅ Detailed report saved to {output_file}")

def run_complexity_analysis(models, data, labels, train_mask):
    """
    Main function to run computational complexity analysis
    """
    analyzer = ComplexityAnalyzer()
    results = analyzer.comprehensive_analysis(models, data, labels, train_mask)
    
    # Create visualizations
    analyzer.visualize_complexity_results(results)
    
    # Generate report
    analyzer.generate_complexity_report(results)
    
    print("\n✅ COMPUTATIONAL COMPLEXITY ANALYSIS COMPLETE")
    print("Key insights:")
    print("  • Parameter efficiency analysis completed")
    print("  • Runtime performance benchmarked")
    print("  • Memory usage profiled")
    print("  • Theoretical complexity characterized")
    print("  • Scalability patterns identified")
    
    return results, analyzer

if __name__ == "__main__":
    print("🚀 COMPUTATIONAL COMPLEXITY ANALYSIS MODULE LOADED")
    print("Available functions:")
    print("  • count_parameters()")
    print("  • measure_inference_time()")
    print("  • measure_training_time()")
    print("  • measure_memory_usage()")
    print("  • theoretical_complexity_analysis()")
    print("  • scalability_analysis()")
    print("  • comprehensive_analysis()")
