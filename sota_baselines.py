#!/usr/bin/env python3
"""
STATE-OF-THE-ART BASELINE MODELS FOR SPAM DETECTION
Addressing Reviewer Comments: Replace traditional ML with modern deep learning baselines
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch_geometric.nn import GCNConv, SAGEConv, GATConv, global_mean_pool
from torch_geometric.data import Data, DataLoader
import numpy as np
from sklearn.metrics import accuracy_score, precision_recall_fscore_support, roc_auc_score
from sklearn.model_selection import StratifiedKFold
import time
import warnings
warnings.filterwarnings('ignore')

class GraphConvolutionalNetwork(nn.Module):
    """
    Graph Convolutional Network (GCN) baseline
    Based on Kipf & Welling (2017)
    """
    def __init__(self, input_dim, hidden_dim=128, num_classes=2, dropout=0.5):
        super().__init__()
        self.conv1 = GCNConv(input_dim, hidden_dim)
        self.conv2 = GCNConv(hidden_dim, hidden_dim)
        self.conv3 = GCNConv(hidden_dim, hidden_dim // 2)
        self.classifier = nn.Linear(hidden_dim // 2, num_classes)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, data):
        x, edge_index = data.x, data.edge_index
        
        x = F.relu(self.conv1(x, edge_index))
        x = self.dropout(x)
        x = F.relu(self.conv2(x, edge_index))
        x = self.dropout(x)
        x = F.relu(self.conv3(x, edge_index))
        x = self.dropout(x)
        
        return self.classifier(x)

class GraphSAGE(nn.Module):
    """
    GraphSAGE baseline
    Based on Hamilton et al. (2017)
    """
    def __init__(self, input_dim, hidden_dim=128, num_classes=2, dropout=0.5):
        super().__init__()
        self.conv1 = SAGEConv(input_dim, hidden_dim)
        self.conv2 = SAGEConv(hidden_dim, hidden_dim)
        self.conv3 = SAGEConv(hidden_dim, hidden_dim // 2)
        self.classifier = nn.Linear(hidden_dim // 2, num_classes)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, data):
        x, edge_index = data.x, data.edge_index
        
        x = F.relu(self.conv1(x, edge_index))
        x = self.dropout(x)
        x = F.relu(self.conv2(x, edge_index))
        x = self.dropout(x)
        x = F.relu(self.conv3(x, edge_index))
        x = self.dropout(x)
        
        return self.classifier(x)

class StandardGAT(nn.Module):
    """
    Standard Graph Attention Network baseline
    Based on Veličković et al. (2018)
    """
    def __init__(self, input_dim, hidden_dim=128, num_classes=2, num_heads=4, dropout=0.5):
        super().__init__()
        self.conv1 = GATConv(input_dim, hidden_dim // num_heads, heads=num_heads, dropout=dropout)
        self.conv2 = GATConv(hidden_dim, hidden_dim // num_heads, heads=num_heads, dropout=dropout)
        self.conv3 = GATConv(hidden_dim, hidden_dim // 2, heads=1, dropout=dropout)
        self.classifier = nn.Linear(hidden_dim // 2, num_classes)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, data):
        x, edge_index = data.x, data.edge_index
        
        x = F.relu(self.conv1(x, edge_index))
        x = self.dropout(x)
        x = F.relu(self.conv2(x, edge_index))
        x = self.dropout(x)
        x = F.relu(self.conv3(x, edge_index))
        x = self.dropout(x)
        
        return self.classifier(x)

class TransformerBaseline(nn.Module):
    """
    Transformer-based baseline for content analysis
    Uses multi-head attention without graph structure
    """
    def __init__(self, input_dim, hidden_dim=128, num_classes=2, num_heads=8, num_layers=3, dropout=0.1):
        super().__init__()
        self.input_projection = nn.Linear(input_dim, hidden_dim)
        
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=hidden_dim,
            nhead=num_heads,
            dim_feedforward=hidden_dim * 4,
            dropout=dropout,
            batch_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)
        
        self.classifier = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 2, num_classes)
        )
        
    def forward(self, data):
        x = data.x
        batch_size = x.size(0)
        
        # Project input to hidden dimension
        x = self.input_projection(x)
        
        # Add positional encoding (simple learned embeddings)
        x = x.unsqueeze(0)  # Add sequence dimension
        
        # Apply transformer
        x = self.transformer(x)
        
        # Global average pooling
        x = x.mean(dim=1).squeeze(0)
        
        return self.classifier(x)

class DeepMLP(nn.Module):
    """
    Deep Multi-Layer Perceptron baseline
    Modern deep learning approach without graph structure
    """
    def __init__(self, input_dim, hidden_dim=256, num_classes=2, num_layers=5, dropout=0.3):
        super().__init__()
        
        layers = []
        current_dim = input_dim
        
        for i in range(num_layers):
            layers.append(nn.Linear(current_dim, hidden_dim))
            layers.append(nn.BatchNorm1d(hidden_dim))
            layers.append(nn.ReLU())
            layers.append(nn.Dropout(dropout))
            current_dim = hidden_dim
            hidden_dim = max(hidden_dim // 2, 32)  # Gradually reduce dimension
        
        layers.append(nn.Linear(current_dim, num_classes))
        self.network = nn.Sequential(*layers)
        
    def forward(self, data):
        x = data.x
        return self.network(x)

class ResidualGNN(nn.Module):
    """
    Residual Graph Neural Network with skip connections
    Modern architecture with residual connections
    """
    def __init__(self, input_dim, hidden_dim=128, num_classes=2, num_layers=4, dropout=0.3):
        super().__init__()
        
        self.input_projection = nn.Linear(input_dim, hidden_dim)
        
        self.gnn_layers = nn.ModuleList()
        self.batch_norms = nn.ModuleList()
        
        for _ in range(num_layers):
            self.gnn_layers.append(GCNConv(hidden_dim, hidden_dim))
            self.batch_norms.append(nn.BatchNorm1d(hidden_dim))
        
        self.classifier = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 2, num_classes)
        )
        
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, data):
        x, edge_index = data.x, data.edge_index
        
        # Project to hidden dimension
        x = self.input_projection(x)
        identity = x
        
        # Apply GNN layers with residual connections
        for i, (gnn_layer, batch_norm) in enumerate(zip(self.gnn_layers, self.batch_norms)):
            residual = x
            x = gnn_layer(x, edge_index)
            x = batch_norm(x)
            x = F.relu(x)
            x = self.dropout(x)
            
            # Add residual connection every 2 layers
            if i % 2 == 1:
                x = x + residual
        
        return self.classifier(x)

def train_model(model, data, labels, train_mask, val_mask, epochs=200, lr=0.01, weight_decay=5e-4):
    """
    Train a model with early stopping and return training history
    """
    optimizer = torch.optim.Adam(model.parameters(), lr=lr, weight_decay=weight_decay)
    criterion = nn.CrossEntropyLoss()
    
    best_val_acc = 0
    patience = 20
    patience_counter = 0
    train_losses = []
    val_accuracies = []
    
    model.train()
    for epoch in range(epochs):
        optimizer.zero_grad()
        out = model(data)
        loss = criterion(out[train_mask], labels[train_mask])
        loss.backward()
        optimizer.step()
        
        # Validation
        model.eval()
        with torch.no_grad():
            val_out = model(data)
            val_pred = val_out[val_mask].argmax(dim=1)
            val_acc = accuracy_score(labels[val_mask].cpu(), val_pred.cpu())
        
        train_losses.append(loss.item())
        val_accuracies.append(val_acc)
        
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            patience_counter = 0
        else:
            patience_counter += 1
            
        if patience_counter >= patience:
            print(f"Early stopping at epoch {epoch}")
            break
        
        model.train()
    
    return train_losses, val_accuracies, best_val_acc

def evaluate_model(model, data, labels, test_mask):
    """
    Evaluate model and return comprehensive metrics
    """
    model.eval()
    start_time = time.time()
    
    with torch.no_grad():
        out = model(data)
        pred = out[test_mask].argmax(dim=1)
        probs = F.softmax(out[test_mask], dim=1)[:, 1]  # Probability of positive class
    
    inference_time = time.time() - start_time
    
    # Calculate metrics
    accuracy = accuracy_score(labels[test_mask].cpu(), pred.cpu())
    precision, recall, f1, _ = precision_recall_fscore_support(
        labels[test_mask].cpu(), pred.cpu(), average='binary'
    )
    auc = roc_auc_score(labels[test_mask].cpu(), probs.cpu())
    
    return {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1': f1,
        'auc': auc,
        'inference_time': inference_time
    }

def get_sota_baselines():
    """
    Return dictionary of state-of-the-art baseline models
    """
    return {
        'GCN': GraphConvolutionalNetwork,
        'GraphSAGE': GraphSAGE,
        'Standard GAT': StandardGAT,
        'Transformer': TransformerBaseline,
        'Deep MLP': DeepMLP,
        'Residual GNN': ResidualGNN
    }

def run_baseline_comparison(data, labels, train_mask, val_mask, test_mask, input_dim):
    """
    Run comprehensive comparison of state-of-the-art baselines
    """
    print("\n🔬 STATE-OF-THE-ART BASELINE COMPARISON")
    print("=" * 60)
    
    baselines = get_sota_baselines()
    results = {}
    
    for name, model_class in baselines.items():
        print(f"\n🧠 Training {name}...")
        
        # Initialize model
        if name == 'Transformer':
            model = model_class(input_dim, hidden_dim=128, num_heads=8)
        elif name == 'Deep MLP':
            model = model_class(input_dim, hidden_dim=256, num_layers=5)
        elif name == 'Residual GNN':
            model = model_class(input_dim, hidden_dim=128, num_layers=4)
        else:
            model = model_class(input_dim, hidden_dim=128)
        
        # Train model
        train_losses, val_accs, best_val_acc = train_model(
            model, data, labels, train_mask, val_mask, epochs=200
        )
        
        # Evaluate model
        metrics = evaluate_model(model, data, labels, test_mask)
        metrics['best_val_acc'] = best_val_acc
        metrics['model_name'] = name
        
        results[name] = metrics
        
        print(f"  ✅ {name}: Acc={metrics['accuracy']:.4f}, F1={metrics['f1']:.4f}, AUC={metrics['auc']:.4f}")
    
    return results

if __name__ == "__main__":
    print("🚀 STATE-OF-THE-ART BASELINES MODULE LOADED")
    print("Available models:", list(get_sota_baselines().keys()))
