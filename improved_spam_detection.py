#!/usr/bin/env python3
"""
IMPROVED SPAM DETECTION SYSTEM - ADDRESSING REVIEWER COMMENTS
Integration of all improvements:
1. ✅ Recipient modeling in data generation
2. ✅ Increased dataset size to prevent data leakage  
3. ✅ State-of-the-art baseline models
4. ✅ Statistical significance testing
5. ✅ Computational complexity analysis
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import classification_report, accuracy_score
import warnings
warnings.filterwarnings('ignore')

# Import our improved modules
from improved_data_generation import generate_improved_synthetic_dataset
from sota_baselines import get_sota_baselines, run_baseline_comparison
from statistical_testing import run_statistical_significance_testing
from complexity_analysis import run_complexity_analysis

# Set seeds for reproducibility
torch.manual_seed(42)
np.random.seed(42)

print("🚀 IMPROVED BEHAVIOR-AWARE SPAM DETECTION SYSTEM")
print("=" * 60)
print("📝 Addressing ALL Reviewer Comments:")
print("  1. ✅ Recipient modeling in data generation")
print("  2. ✅ Increased dataset size (2000 users vs 500)")
print("  3. ✅ State-of-the-art baseline models")
print("  4. ✅ Statistical significance testing")
print("  5. ✅ Computational complexity analysis")
print("  6. ✅ Architecture remains the same (as requested)")

class BehaviorAwareGAT(nn.Module):
    """
    ORIGINAL Behavior-Aware GAT: Architecture UNCHANGED as requested
    """
    def __init__(self, content_dim, temporal_dim, structural_dim,
                 hidden_dim=128, num_heads=4, num_classes=2, dropout=0.3):
        super().__init__()
        
        # Behavioral change detection module
        self.temporal_change_detector = nn.Sequential(
            nn.Linear(temporal_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 2, hidden_dim // 4)
        )
        
        # Structural position analyzer
        self.structural_analyzer = nn.Sequential(
            nn.Linear(structural_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 2, hidden_dim // 4)
        )
        
        # Content analyzer (BERT-based, activated by temporal changes)
        if content_dim > 100:  # BERT embeddings
            self.content_analyzer = nn.Sequential(
                nn.Linear(content_dim, hidden_dim),
                nn.ReLU(),
                nn.Dropout(dropout),
                nn.Linear(hidden_dim, hidden_dim // 2),
                nn.ReLU(),
                nn.Dropout(dropout),
                nn.Linear(hidden_dim // 2, hidden_dim // 4)
            )
        else:  # Traditional handcrafted features
            self.content_analyzer = nn.Sequential(
                nn.Linear(content_dim, hidden_dim // 2),
                nn.ReLU(),
                nn.Dropout(dropout),
                nn.Linear(hidden_dim // 2, hidden_dim // 4)
            )
        
        # Behavioral change attention mechanism
        self.change_attention = nn.MultiheadAttention(hidden_dim // 4, num_heads=2, dropout=dropout)
        
        # Cross-modal attention for temporal → structural → content flow
        fusion_dim = 3 * (hidden_dim // 4)  # 3 feature types
        self.cross_modal_attention = nn.MultiheadAttention(fusion_dim, num_heads=3, dropout=dropout)
        
        # Project fused features to hidden_dim for GAT layers
        self.feature_projection = nn.Linear(fusion_dim, hidden_dim)
        
        # Enhanced GAT layers with behavioral awareness
        from torch_geometric.nn import GATConv
        self.gat1 = GATConv(hidden_dim, hidden_dim, heads=num_heads, dropout=dropout, concat=True)
        self.gat2 = GATConv(hidden_dim * num_heads, hidden_dim, heads=1, dropout=dropout)
        
        # Behavioral fusion layer
        self.behavioral_fusion = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        # Final classifier with behavioral context
        self.classifier = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 2, hidden_dim // 4),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 4, num_classes)
        )
        
        self.dropout = nn.Dropout(dropout)
    
    def forward(self, data):
        content_features = data.content_features
        temporal_features = data.temporal_features
        structural_features = data.structural_features
        edge_index = data.edge_index
        
        # Step 1: Detect temporal behavioral changes
        temporal_repr = self.temporal_change_detector(temporal_features)
        
        # Step 2: Analyze structural position (influenced by temporal changes)
        structural_repr = self.structural_analyzer(structural_features)
        
        # Step 3: Apply change-aware attention to structural analysis
        temporal_attended, _ = self.change_attention(
            structural_repr.unsqueeze(0),
            temporal_repr.unsqueeze(0),
            temporal_repr.unsqueeze(0)
        )
        structural_repr = temporal_attended.squeeze(0)
        
        # Step 4: Content analysis (activated by temporal changes)
        content_repr = self.content_analyzer(content_features)
        
        # Step 5: Behavioral flow: Temporal → Structural → Content
        behavioral_features = torch.cat([temporal_repr, structural_repr, content_repr], dim=1)
        
        # Step 6: Cross-modal attention for behavioral understanding
        behavioral_attended, _ = self.cross_modal_attention(
            behavioral_features.unsqueeze(0),
            behavioral_features.unsqueeze(0),
            behavioral_features.unsqueeze(0)
        )
        x = behavioral_attended.squeeze(0)
        
        # Project to hidden_dim for GAT layers
        x = self.feature_projection(x)
        
        # Step 7: Graph attention with behavioral context
        x = F.relu(self.gat1(x, edge_index))
        x = self.dropout(x)
        x = self.gat2(x, edge_index)
        
        # Step 8: Behavioral fusion
        x = self.behavioral_fusion(x)
        
        # Step 9: Final classification
        out = self.classifier(x)
        return out

def extract_improved_features(users, messages, social_connections):
    """
    Extract features with recipient awareness and improved modeling
    """
    print("\n🔧 EXTRACTING IMPROVED FEATURES WITH RECIPIENT MODELING")
    print("=" * 60)
    
    from transformers import AutoTokenizer, AutoModel
    import networkx as nx
    from datetime import datetime, timedelta
    
    # Initialize BERT for content analysis
    print("  • Loading BERT model for content analysis...")
    tokenizer = AutoTokenizer.from_pretrained('distilbert-base-uncased')
    bert_model = AutoModel.from_pretrained('distilbert-base-uncased')
    
    user_list = list(users.keys())
    num_users = len(user_list)
    
    # 1. IMPROVED CONTENT FEATURES WITH RECIPIENT AWARENESS
    print("  • Extracting recipient-aware content features...")
    content_features = []
    
    for user_id in user_list:
        user_messages = [m for m in messages if m['sender_id'] == user_id]
        
        if user_messages:
            # Separate messages by recipient type
            connected_messages = [m['content'] for m in user_messages if m['is_connected']]
            unconnected_messages = [m['content'] for m in user_messages if not m['is_connected']]
            
            # Combine up to 5 messages of each type
            combined_text = " ".join(connected_messages[:5] + unconnected_messages[:5])
            
            # BERT encoding
            inputs = tokenizer(combined_text, return_tensors='pt', truncation=True, 
                             padding=True, max_length=512)
            with torch.no_grad():
                outputs = bert_model(**inputs)
                content_embedding = outputs.last_hidden_state[:, 0, :].squeeze().numpy()
        else:
            content_embedding = np.zeros(768)  # DistilBERT dimension
        
        content_features.append(content_embedding)
    
    content_features = np.array(content_features)
    
    # 2. IMPROVED TEMPORAL FEATURES WITH RECIPIENT PATTERNS
    print("  • Extracting recipient-aware temporal features...")
    temporal_features = []
    
    for user_id in user_list:
        user_messages = [m for m in messages if m['sender_id'] == user_id]
        
        if user_messages:
            # Basic temporal features
            timestamps = [m['timestamp'] for m in user_messages]
            hours = [t.hour for t in timestamps]
            
            # Recipient-aware temporal patterns
            connected_msgs = [m for m in user_messages if m['is_connected']]
            unconnected_msgs = [m for m in user_messages if not m['is_connected']]
            
            features = [
                len(user_messages),  # Total messages
                np.std(hours) if len(hours) > 1 else 0,  # Activity variance
                sum(1 for h in hours if 23 <= h or h <= 5),  # Night activity
                len(connected_msgs),  # Messages to connected users
                len(unconnected_msgs),  # Messages to strangers
                len(connected_msgs) / len(user_messages) if user_messages else 0,  # Connection ratio
                np.mean([m['is_spam'] for m in connected_msgs]) if connected_msgs else 0,  # Spam rate to connected
                np.mean([m['is_spam'] for m in unconnected_msgs]) if unconnected_msgs else 0,  # Spam rate to strangers
            ]
        else:
            features = [0] * 8
        
        temporal_features.append(features)
    
    temporal_features = np.array(temporal_features)
    
    # 3. IMPROVED STRUCTURAL FEATURES WITH RECIPIENT NETWORK ANALYSIS
    print("  • Extracting recipient-aware structural features...")
    
    # Build network graph
    G = nx.Graph()
    G.add_nodes_from(user_list)
    
    for user_id, connections in social_connections.items():
        for connected_user in connections:
            G.add_edge(user_id, connected_user)
    
    structural_features = []
    
    for user_id in user_list:
        # Basic centrality measures
        degree_centrality = nx.degree_centrality(G)[user_id]
        betweenness_centrality = nx.betweenness_centrality(G)[user_id]
        clustering_coefficient = nx.clustering(G, user_id)
        
        # Recipient-aware features
        neighbors = list(G.neighbors(user_id))
        compromised_neighbors = sum(1 for n in neighbors if users[n]['is_compromised'])
        neighbor_ratio = compromised_neighbors / len(neighbors) if neighbors else 0
        
        # Message recipient analysis
        user_messages = [m for m in messages if m['sender_id'] == user_id]
        recipients = [m['recipient_id'] for m in user_messages]
        unique_recipients = len(set(recipients))
        recipient_diversity = unique_recipients / len(recipients) if recipients else 0
        
        # Community analysis
        same_community_msgs = sum(1 for m in user_messages if m['same_community'])
        cross_community_ratio = (len(user_messages) - same_community_msgs) / len(user_messages) if user_messages else 0
        
        features = [
            degree_centrality,
            betweenness_centrality,
            clustering_coefficient,
            neighbor_ratio,
            recipient_diversity,
            cross_community_ratio,
            len(neighbors),
            unique_recipients
        ]
        
        structural_features.append(features)
    
    structural_features = np.array(structural_features)
    
    print(f"  ✅ Feature extraction complete:")
    print(f"    • Content features: {content_features.shape}")
    print(f"    • Temporal features: {temporal_features.shape}")
    print(f"    • Structural features: {structural_features.shape}")
    
    return content_features, temporal_features, structural_features

def main():
    """
    Main function integrating all improvements
    """
    print("\n🎯 RUNNING COMPREHENSIVE IMPROVED SPAM DETECTION ANALYSIS")
    print("=" * 60)
    
    # 1. Generate improved synthetic dataset
    print("\n📊 STEP 1: IMPROVED DATA GENERATION")
    users, messages, interactions, social_connections = generate_improved_synthetic_dataset()
    
    # 2. Extract improved features
    print("\n🔧 STEP 2: IMPROVED FEATURE EXTRACTION")
    content_features, temporal_features, structural_features = extract_improved_features(
        users, messages, social_connections
    )
    
    # 3. Prepare data for models
    print("\n⚙️ STEP 3: DATA PREPARATION")
    
    # Create labels
    user_list = list(users.keys())
    labels = torch.tensor([users[user_id]['is_compromised'] for user_id in user_list], dtype=torch.long)
    
    # Create edge index from social connections
    edge_list = []
    user_to_idx = {user_id: idx for idx, user_id in enumerate(user_list)}
    
    for user_id, connections in social_connections.items():
        for connected_user in connections:
            if connected_user in user_to_idx:  # Ensure both users exist
                edge_list.append([user_to_idx[user_id], user_to_idx[connected_user]])
    
    edge_index = torch.tensor(edge_list, dtype=torch.long).t().contiguous()
    
    # Create data object
    from torch_geometric.data import Data
    
    # Combine all features
    all_features = np.concatenate([content_features, temporal_features, structural_features], axis=1)
    
    data = Data(
        x=torch.tensor(all_features, dtype=torch.float),
        edge_index=edge_index,
        content_features=torch.tensor(content_features, dtype=torch.float),
        temporal_features=torch.tensor(temporal_features, dtype=torch.float),
        structural_features=torch.tensor(structural_features, dtype=torch.float)
    )
    
    # Create train/test splits
    train_mask = torch.zeros(len(labels), dtype=torch.bool)
    test_mask = torch.zeros(len(labels), dtype=torch.bool)
    
    train_indices, test_indices = train_test_split(
        range(len(labels)), test_size=0.2, stratify=labels, random_state=42
    )
    
    train_mask[train_indices] = True
    test_mask[test_indices] = True
    
    print(f"  ✅ Data prepared: {len(user_list)} users, {edge_index.size(1)} edges")
    print(f"  • Training set: {train_mask.sum()} users")
    print(f"  • Test set: {test_mask.sum()} users")
    
    # 4. Initialize models (including our improved GAT)
    print("\n🧠 STEP 4: MODEL INITIALIZATION")
    
    # Get state-of-the-art baselines
    baseline_classes = get_sota_baselines()
    
    # Initialize all models
    models = {}
    input_dim = all_features.shape[1]
    
    for name, model_class in baseline_classes.items():
        models[name] = model_class(input_dim)
    
    # Add our improved Behavior-Aware GAT
    models['Behavior-Aware GAT (Ours)'] = BehaviorAwareGAT(
        content_dim=content_features.shape[1],
        temporal_dim=temporal_features.shape[1],
        structural_dim=structural_features.shape[1]
    )
    
    print(f"  ✅ Initialized {len(models)} models")
    
    # 5. Run comprehensive evaluation
    print("\n📈 STEP 5: COMPREHENSIVE EVALUATION")
    
    # Baseline comparison
    baseline_results = run_baseline_comparison(data, labels, train_mask, test_mask, test_mask, input_dim)
    
    # Statistical significance testing
    statistical_results, tester = run_statistical_significance_testing(models, data, labels)
    
    # Computational complexity analysis
    complexity_results, analyzer = run_complexity_analysis(models, data, labels, train_mask)
    
    print("\n🎉 COMPREHENSIVE ANALYSIS COMPLETE!")
    print("=" * 60)
    print("📋 SUMMARY OF IMPROVEMENTS:")
    print("  1. ✅ Dataset size increased from 500 to 2000 users")
    print("  2. ✅ Recipient modeling added to data generation")
    print("  3. ✅ State-of-the-art baselines implemented")
    print("  4. ✅ Statistical significance testing performed")
    print("  5. ✅ Computational complexity analysis completed")
    print("  6. ✅ Original architecture preserved")
    
    return {
        'data': data,
        'labels': labels,
        'models': models,
        'baseline_results': baseline_results,
        'statistical_results': statistical_results,
        'complexity_results': complexity_results
    }

if __name__ == "__main__":
    results = main()
